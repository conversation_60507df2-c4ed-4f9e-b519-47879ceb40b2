package common

import (
	"time"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID          int       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	CreatedBy   string    `json:"created_by" gorm:"size:255" db:"created_by"`
	CreatedByID string    `json:"created_by_id" gorm:"size:255" db:"created_by_id"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	UpdatedBy   string    `json:"updated_by" gorm:"size:255" db:"updated_by"`
	UpdatedByID string    `json:"updated_by_id" gorm:"size:255" db:"updated_by_id"`
}

// MemberTier represents the membertier table
type MemberTier struct {
	BaseModel
	DisplayName string `json:"display_name" gorm:"size:255" db:"display_name"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	Status      string `json:"status" gorm:"size:255" db:"status"`
}

// CustomerGroup represents the customer_group table
type CustomerGroup struct {
	BaseModel
	Name        string `json:"name" gorm:"size:255" db:"name"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	Status      string `json:"status" gorm:"size:255" db:"status"`
}

// Customer represents the customer table
type Customer struct {
	BaseModel
	GroupID               *int       `json:"group_id" gorm:"index" db:"group_id"`
	MemberTierID          *int       `json:"membertier_id" gorm:"index" db:"membertier_id"`
	ShopIDs               *int       `json:"shop_ids" gorm:"index" db:"shop_ids"`
	ProductGroupIDs       *int       `json:"product_group_ids" gorm:"index" db:"product_group_ids"`
	FirstName             string     `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName              string     `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                 string     `json:"email" gorm:"size:255;index" db:"email"`
	Phone                 string     `json:"phone" gorm:"size:255;index" db:"phone"`
	LineID                string     `json:"line_id" gorm:"size:255" db:"line_id"`
	Gender                string     `json:"gender" gorm:"size:50" db:"gender"`
	DateOfBirth           *time.Time `json:"date_of_birth" db:"date_of_birth"`
	AddressDistrict       string     `json:"address_district" gorm:"size:255" db:"address_district"`
	AddressProvince       string     `json:"address_province" gorm:"size:255" db:"address_province"`
	Education             string     `json:"education" gorm:"size:255" db:"education"`
	Job                   string     `json:"job" gorm:"size:255" db:"job"`
	Salary                string     `json:"salary" gorm:"size:255" db:"salary"`
	ProfileURL            string     `json:"profile_url" gorm:"size:500" db:"profile_url"`
	ProfileURLContentType string     `json:"profile_url_content_type" gorm:"size:100" db:"profile_url_content_type"`
	Point                 int        `json:"point" gorm:"default:0" db:"point"`
	CreditScore           float64    `json:"credit_score" gorm:"type:decimal(10,2)" db:"credit_score"`
	ReferenceCode         string     `json:"reference_code" gorm:"size:255;uniqueIndex" db:"reference_code"`
	Status                string     `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group      *CustomerGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	MemberTier *MemberTier    `json:"member_tier,omitempty" gorm:"foreignKey:MemberTierID"`
	Shop       *Shop          `json:"shop,omitempty" gorm:"foreignKey:ShopIDs"`
}

// CustomerGrouping represents the customer_groupping table
type CustomerGrouping struct {
	ID           int       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	CustomerIDs  *int      `json:"customer_ids" gorm:"index" db:"customer_ids"`
	MemberTierID *int      `json:"membertier_id" gorm:"index" db:"membertier_id"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime" db:"created_at"`
	CreatedBy    string    `json:"created_by" gorm:"size:255" db:"created_by"`
	CreatedByID  string    `json:"created_by_id" gorm:"size:255" db:"created_by_id"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
	UpdatedBy    string    `json:"updated_by" gorm:"size:255" db:"updated_by"`
	UpdatedByID  string    `json:"updated_by_id" gorm:"size:255" db:"updated_by_id"`

	// Relationships
	Customer   *Customer   `json:"customer,omitempty" gorm:"foreignKey:CustomerIDs"`
	MemberTier *MemberTier `json:"member_tier,omitempty" gorm:"foreignKey:MemberTierID"`
}

// TncPdpa represents the tnc_pdpa table
type TncPdpa struct {
	BaseModel
	UserType     string     `json:"user_type" gorm:"size:100" db:"user_type"`
	ConsentType  string     `json:"consent_type" gorm:"size:100" db:"consent_type"`
	TermsVersion string     `json:"terms_version" gorm:"size:50" db:"terms_version"`
	Title        string     `json:"title" gorm:"size:500" db:"title"`
	Content      string     `json:"content" gorm:"type:text" db:"content"`
	Status       string     `json:"status" gorm:"size:255" db:"status"`
	ReleaseDate  *time.Time `json:"release_date" db:"release_date"`
}

// TncPdpaConsent represents the tnc_pdpa_consent table
type TncPdpaConsent struct {
	BaseModel
	TncID         *int       `json:"tnc_id" gorm:"index" db:"tnc_id"`
	UserID        *int       `json:"user_id" gorm:"index" db:"user_id"`
	UserType      string     `json:"user_type" gorm:"size:100" db:"user_type"`
	ConsentType   string     `json:"consent_type" gorm:"size:100" db:"consent_type"`
	TermsVersion  string     `json:"terms_version" gorm:"size:50" db:"terms_version"`
	TermsSnapshot string     `json:"terms_snapshot" gorm:"type:text" db:"terms_snapshot"`
	TermsAccept   bool       `json:"terms_accept" gorm:"default:false" db:"terms_accept"`
	AcceptedAt    *time.Time `json:"accepted_at" db:"accepted_at"`

	// Relationships
	Tnc *TncPdpa `json:"tnc,omitempty" gorm:"foreignKey:TncID"`
}

// Role represents the role table
type Role struct {
	BaseModel
	Name        string   `json:"name" gorm:"size:255" db:"name"`
	Permissions []string `json:"permissions" gorm:"type:text[]" db:"permissions"`
}

// Shop represents the shop table
type Shop struct {
	BaseModel
	Name               string `json:"name" gorm:"size:255" db:"name"`
	AddressLine1       string `json:"address_line1" gorm:"size:500" db:"address_line1"`
	AddressSubdistrict string `json:"address_subdistrict" gorm:"size:255" db:"address_subdistrict"`
	AddressDistrict    string `json:"address_district" gorm:"size:255" db:"address_district"`
	AddressProvince    string `json:"address_province" gorm:"size:255" db:"address_province"`
	AddressZipcode     string `json:"address_zipcode" gorm:"size:20" db:"address_zipcode"`
	AreaID             string `json:"area_id" gorm:"size:255" db:"area_id"`
	Status             string `json:"status" gorm:"size:255" db:"status"`
}

// Staff represents the staff table
type Staff struct {
	BaseModel
	ShopID                *int       `json:"shop_id" gorm:"index" db:"shop_id"`
	AreaID                *int       `json:"area_id" gorm:"index" db:"area_id"`
	SupervisorID          *int       `json:"supervisor_id" gorm:"index" db:"supervisor_id"`
	RoleID                *int       `json:"role_id" gorm:"index" db:"role_id"`
	Permissions           []string   `json:"permissions" gorm:"type:text[]" db:"permissions"`
	EmployeeID            string     `json:"employee_id" gorm:"size:255;uniqueIndex" db:"employee_id"`
	FirstName             string     `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName              string     `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                 string     `json:"email" gorm:"size:255;index" db:"email"`
	LineToken             string     `json:"line_token" gorm:"size:500" db:"line_token"`
	Phone                 string     `json:"phone" gorm:"size:255;index" db:"phone"`
	Gender                string     `json:"gender" gorm:"size:50" db:"gender"`
	Education             string     `json:"education" gorm:"size:255" db:"education"`
	DateOfBirth           *time.Time `json:"date_of_birth" db:"date_of_birth"`
	ProfileURL            string     `json:"profile_url" gorm:"size:500" db:"profile_url"`
	ProfileURLContentType string     `json:"profile_url_content_type" gorm:"size:100" db:"profile_url_content_type"`
	ReferenceCode         string     `json:"reference_code" gorm:"size:255;uniqueIndex" db:"reference_code"`
	Status                string     `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Shop       *Shop  `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Role       *Role  `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	Supervisor *Staff `json:"supervisor,omitempty" gorm:"foreignKey:SupervisorID"`
}

// CheckIn represents the checkin table
type CheckIn struct {
	BaseModel
	StaffID   *int       `json:"staff_id" gorm:"index" db:"staff_id"`
	Lat       float64    `json:"lat" gorm:"type:decimal(10,8)" db:"lat"`
	Lng       float64    `json:"lng" gorm:"type:decimal(11,8)" db:"lng"`
	CheckedAt *time.Time `json:"checked_at" db:"checked_at"`

	// Relationships
	Staff *Staff `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
}

// ProductGroup represents the product_group table
type ProductGroup struct {
	BaseModel
	Name        string `json:"name" gorm:"size:255" db:"name"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	Status      string `json:"status" gorm:"size:255" db:"status"`
}

// Product represents the product table
type Product struct {
	BaseModel
	GroupID      *int    `json:"group_id" gorm:"index" db:"group_id"`
	Name         string  `json:"name" gorm:"size:255" db:"name"`
	Description  string  `json:"description" gorm:"type:text" db:"description"`
	SalePrice    float64 `json:"sale_price" gorm:"type:decimal(10,2)" db:"sale_price"`
	RegularPrice float64 `json:"regular_price" gorm:"type:decimal(10,2)" db:"regular_price"`
	Brand        string  `json:"brand" gorm:"size:255" db:"brand"`
	TypeOfDevice string  `json:"type_of_device" gorm:"size:255" db:"type_of_device"`
	Status       string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group *ProductGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
}

// PromotionCategory represents the promotion_category table
type PromotionCategory struct {
	BaseModel
	Name        string `json:"name" gorm:"size:255" db:"name"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	Status      string `json:"status" gorm:"size:255" db:"status"`
}

// Promotion represents the promotion table
type Promotion struct {
	BaseModel
	CategoryID           *int       `json:"category_id" gorm:"index" db:"category_id"`
	ProductID            *int       `json:"product_id" gorm:"index" db:"product_id"`
	Title                string     `json:"title" gorm:"size:500" db:"title"`
	Description          string     `json:"description" gorm:"type:text" db:"description"`
	Condition            string     `json:"condition" gorm:"type:text" db:"condition"`
	StartDate            *time.Time `json:"start_date" db:"start_date"`
	EndDate              *time.Time `json:"end_date" db:"end_date"`
	BannerURL            string     `json:"banner_url" gorm:"size:500" db:"banner_url"`
	BannerURLContentType string     `json:"banner_url_content_type" gorm:"size:100" db:"banner_url_content_type"`
	Status               string     `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Category *PromotionCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Product  *Product           `json:"product,omitempty" gorm:"foreignKey:ProductID"`
}

// PromotionTier represents the promotion_tier table
type PromotionTier struct {
	BaseModel
	PromotionID   *int    `json:"promotion_id" gorm:"index" db:"promotion_id"`
	Description   string  `json:"description" gorm:"type:text" db:"description"`
	MinAmount     float64 `json:"min_amount" gorm:"type:decimal(10,2)" db:"min_amount"`
	MaxAmount     float64 `json:"max_amount" gorm:"type:decimal(10,2)" db:"max_amount"`
	DiscountType  string  `json:"discount_type" gorm:"size:100" db:"discount_type"`
	DiscountValue float64 `json:"discount_value" gorm:"type:decimal(10,2)" db:"discount_value"`
	Status        string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Promotion *Promotion `json:"promotion,omitempty" gorm:"foreignKey:PromotionID"`
}

// PurchaseLog represents the purchase_log table
type PurchaseLog struct {
	BaseModel
	CustomerID      *int    `json:"customer_id" gorm:"index" db:"customer_id"`
	ShopID          *int    `json:"shop_id" gorm:"index" db:"shop_id"`
	StaffID         *int    `json:"staff_id" gorm:"index" db:"staff_id"`
	ProductID       *int    `json:"product_id" gorm:"index" db:"product_id"`
	PromotionID     *int    `json:"promotion_id" gorm:"index" db:"promotion_id"`
	PromotionTierID *int    `json:"promotion_tier_id" gorm:"index" db:"promotion_tier_id"`
	Price           float64 `json:"price" gorm:"type:decimal(10,2)" db:"price"`
	Status          string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Customer      *Customer      `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Shop          *Shop          `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Staff         *Staff         `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
	Product       *Product       `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Promotion     *Promotion     `json:"promotion,omitempty" gorm:"foreignKey:PromotionID"`
	PromotionTier *PromotionTier `json:"promotion_tier,omitempty" gorm:"foreignKey:PromotionTierID"`
}

// CmsLead represents the cms_lead table
type CmsLead struct {
	BaseModel
	GroupID              *int    `json:"group_id" gorm:"index" db:"group_id"`
	CustomerID           *int    `json:"customer_id" gorm:"index" db:"customer_id"`
	StaffID              *int    `json:"staff_id" gorm:"index" db:"staff_id"`
	FirstName            string  `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName             string  `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                string  `json:"email" gorm:"size:255;index" db:"email"`
	Phone                string  `json:"phone" gorm:"size:255;index" db:"phone"`
	LineID               string  `json:"lineId" gorm:"size:255" db:"lineId"`
	Provider             string  `json:"provider" gorm:"size:255" db:"provider"`
	Price                float64 `json:"price" gorm:"type:decimal(10,2)" db:"price"`
	DurationMonths       int     `json:"duration_months" db:"duration_months"`
	ReasonNotSwitch      string  `json:"reason_not_switch" gorm:"type:text" db:"reason_not_switch"`
	Notes                string  `json:"notes" gorm:"type:text" db:"notes"`
	PreferredContactTime string  `json:"preferred_contact_time" gorm:"size:255" db:"preferred_contact_time"`
	Status               string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group    *CustomerGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Customer *Customer      `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Staff    *Staff         `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
}

// SolarLead represents the solar_lead table
type SolarLead struct {
	BaseModel
	GroupID              *int    `json:"group_id" gorm:"index" db:"group_id"`
	CustomerID           *int    `json:"customer_id" gorm:"index" db:"customer_id"`
	StaffID              *int    `json:"staff_id" gorm:"index" db:"staff_id"`
	FirstName            string  `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName             string  `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                string  `json:"email" gorm:"size:255;index" db:"email"`
	Phone                string  `json:"phone" gorm:"size:255;index" db:"phone"`
	LineID               string  `json:"lineId" gorm:"size:255" db:"lineId"`
	MonthlyElectricBill  float64 `json:"monthly_electric_bill" gorm:"type:decimal(10,2)" db:"monthly_electric_bill"`
	ResidenceType        string  `json:"residence_type" gorm:"size:255" db:"residence_type"`
	Meter                string  `json:"meter" gorm:"size:255" db:"meter"`
	Roof                 string  `json:"roof" gorm:"size:255" db:"roof"`
	RoofType             string  `json:"roof_type" gorm:"size:255" db:"roof_type"`
	RoofRemark           string  `json:"roof_remark" gorm:"type:text" db:"roof_remark"`
	ConcurrentUsers      int     `json:"concurrent_users" db:"concurrent_users"`
	ConcurrentAir        string  `json:"concurrent_air" gorm:"size:255" db:"concurrent_air"`
	ElectricityUsage     string  `json:"electricity_usage" gorm:"size:255" db:"electricity_usage"`
	Notes                string  `json:"notes" gorm:"type:text" db:"notes"`
	PreferredContactTime string  `json:"preferred_contact_time" gorm:"size:255" db:"preferred_contact_time"`
	Status               string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group    *CustomerGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Customer *Customer      `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Staff    *Staff         `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
}

// TechLead represents the tech_lead table
type TechLead struct {
	BaseModel
	GroupID              *int    `json:"group_id" gorm:"index" db:"group_id"`
	CustomerID           *int    `json:"customer_id" gorm:"index" db:"customer_id"`
	StaffID              *int    `json:"staff_id" gorm:"index" db:"staff_id"`
	FirstName            string  `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName             string  `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                string  `json:"email" gorm:"size:255;index" db:"email"`
	Phone                string  `json:"phone" gorm:"size:255;index" db:"phone"`
	LineID               string  `json:"lineId" gorm:"size:255" db:"lineId"`
	InstallationAreaType string  `json:"installation_area_type" gorm:"size:255" db:"installation_area_type"`
	ResidenceType        string  `json:"residence_type" gorm:"size:255" db:"residence_type"`
	Floors               int     `json:"floors" db:"floors"`
	PrimaryUse           string  `json:"primary_use" gorm:"size:255" db:"primary_use"`
	UsageType            string  `json:"usage_type" gorm:"size:255" db:"usage_type"`
	ConcurrentUsers      int     `json:"concurrent_users" db:"concurrent_users"`
	PrimaryTime          string  `json:"primary_time" gorm:"size:255" db:"primary_time"`
	Equipment            string  `json:"equipment" gorm:"size:255" db:"equipment"`
	AccessPointQty       int     `json:"access_point_qty" db:"access_point_qty"`
	AccessPointRemark    string  `json:"access_point_remark" gorm:"type:text" db:"access_point_remark"`
	IsHasSmartHome       bool    `json:"is_has_smart_home" gorm:"default:false" db:"is_has_smart_home"`
	SmartHomeRemark      string  `json:"mart_home_remark" gorm:"type:text" db:"mart_home_remark"`
	IsInterestedSolar    bool    `json:"is_interested_solar" gorm:"default:false" db:"is_interested_solar"`
	MonthlyElectricBill  float64 `json:"monthly_electric_bill" gorm:"type:decimal(10,2)" db:"monthly_electric_bill"`
	ProductIDs           *int    `json:"product_ids" gorm:"index" db:"product_ids"`
	Notes                string  `json:"notes" gorm:"type:text" db:"notes"`
	PreferredContactTime string  `json:"preferred_contact_time" gorm:"size:255" db:"preferred_contact_time"`
	Status               string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group    *CustomerGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Customer *Customer      `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Staff    *Staff         `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
	Product  *Product       `json:"product,omitempty" gorm:"foreignKey:ProductIDs"`
}

// TechRegis represents the tech_regis table
type TechRegis struct {
	BaseModel
	GroupID              *int    `json:"group_id" gorm:"index" db:"group_id"`
	CustomerID           *int    `json:"customer_id" gorm:"index" db:"customer_id"`
	StaffID              *int    `json:"staff_id" gorm:"index" db:"staff_id"`
	FirstName            string  `json:"firstname" gorm:"size:255" db:"firstname"`
	LastName             string  `json:"lastname" gorm:"size:255" db:"lastname"`
	Email                string  `json:"email" gorm:"size:255;index" db:"email"`
	Phone                string  `json:"phone" gorm:"size:255;index" db:"phone"`
	LineID               string  `json:"lineId" gorm:"size:255" db:"lineId"`
	ProductIDs           *int    `json:"product_ids" gorm:"index" db:"product_ids"`
	PromotionIDs         *int    `json:"promotion_ids" gorm:"index" db:"promotion_ids"`
	Provider             string  `json:"provider" gorm:"size:255" db:"provider"`
	Price                float64 `json:"price" gorm:"type:decimal(10,2)" db:"price"`
	ResidenceType        string  `json:"residence_type" gorm:"size:255" db:"residence_type"`
	Notes                string  `json:"notes" gorm:"type:text" db:"notes"`
	PreferredContactTime string  `json:"preferred_contact_time" gorm:"size:255" db:"preferred_contact_time"`
	Status               string  `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Group     *CustomerGroup `json:"group,omitempty" gorm:"foreignKey:GroupID"`
	Customer  *Customer      `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Staff     *Staff         `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
	Product   *Product       `json:"product,omitempty" gorm:"foreignKey:ProductIDs"`
	Promotion *Promotion     `json:"promotion,omitempty" gorm:"foreignKey:PromotionIDs"`
}

// LeadLog represents the lead_log table
type LeadLog struct {
	BaseModel
	CustomerID    *int   `json:"customer_id" gorm:"index" db:"customer_id"`
	StaffID       *int   `json:"staff_id" gorm:"index" db:"staff_id"`
	PromotionID   *int   `json:"promotion_id" gorm:"index" db:"promotion_id"`
	CustomerPhone string `json:"customer_phone" gorm:"size:255;index" db:"customer_phone"`
	Remark        string `json:"remark" gorm:"type:text" db:"remark"`
	Reason        string `json:"reason" gorm:"type:text" db:"reason"`
	OtherReason   string `json:"other_reason" gorm:"type:text" db:"other_reason"`
	IsPotential   bool   `json:"is_potential" gorm:"default:false" db:"is_potential"`
	Status        string `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Customer  *Customer  `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
	Staff     *Staff     `json:"staff,omitempty" gorm:"foreignKey:StaffID"`
	Promotion *Promotion `json:"promotion,omitempty" gorm:"foreignKey:PromotionID"`
}

// ContactUs represents the contact_us table
type ContactUs struct {
	BaseModel
	Email       string `json:"email" gorm:"size:255;index" db:"email"`
	Phone       string `json:"phone" gorm:"size:255;index" db:"phone"`
	Facebook    string `json:"facebook" gorm:"size:500" db:"facebook"`
	Instagram   string `json:"instagram" gorm:"size:500" db:"instagram"`
	TikTok      string `json:"tiktok" gorm:"size:500" db:"tiktok"`
	LineID      string `json:"line_id" gorm:"size:255" db:"line_id"`
	LineURL     string `json:"line_url" gorm:"size:500" db:"line_url"`
	YouTube     string `json:"youtube" gorm:"size:500" db:"youtube"`
	Twitter     string `json:"twitter" gorm:"size:500" db:"twitter"`
	LinkedIn    string `json:"linkedin" gorm:"size:500" db:"linkedin"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	FAQ         string `json:"faq" gorm:"type:text" db:"faq"`
	Policy      string `json:"policy" gorm:"type:text" db:"policy"`
}

// PointSetting represents the point_setting table
type PointSetting struct {
	BaseModel
	Price               float64 `json:"price" gorm:"type:decimal(10,2)" db:"price"`
	ExpiredAfterReceive int     `json:"expired_after_receive" db:"expired_after_receive"`
}

// PointTransaction represents the point_transaction table
type PointTransaction struct {
	BaseModel
	CustomerID *int       `json:"customer_id" gorm:"index" db:"customer_id"`
	Point      int        `json:"point" db:"point"`
	ActionType string     `json:"action_type" gorm:"size:255" db:"action_type"`
	Remain     int        `json:"remain" db:"remain"`
	ExpiredAt  *time.Time `json:"expired_at" db:"expired_at"`

	// Relationships
	Customer *Customer `json:"customer,omitempty" gorm:"foreignKey:CustomerID"`
}

// TopicCategory represents the topic_category table
type TopicCategory struct {
	BaseModel
	Name        string `json:"name" gorm:"size:255" db:"name"`
	Description string `json:"description" gorm:"type:text" db:"description"`
	Status      string `json:"status" gorm:"size:255" db:"status"`
}

// FAQ represents the faq table
type FAQ struct {
	BaseModel
	CategoryID *int   `json:"category_id" gorm:"index" db:"category_id"`
	Question   string `json:"question" gorm:"type:text" db:"question"`
	Answer     string `json:"answer" gorm:"type:text" db:"answer"`
	Status     string `json:"status" gorm:"size:255" db:"status"`

	// Relationships
	Category *TopicCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName methods to specify custom table names
func (MemberTier) TableName() string        { return "membertier" }
func (CustomerGroup) TableName() string     { return "customer_group" }
func (Customer) TableName() string          { return "customer" }
func (CustomerGrouping) TableName() string  { return "customer_groupping" }
func (TncPdpa) TableName() string           { return "tnc_pdpa" }
func (TncPdpaConsent) TableName() string    { return "tnc_pdpa_consent" }
func (Role) TableName() string              { return "role" }
func (Shop) TableName() string              { return "shop" }
func (Staff) TableName() string             { return "staff" }
func (CheckIn) TableName() string           { return "checkin" }
func (ProductGroup) TableName() string      { return "product_group" }
func (Product) TableName() string           { return "product" }
func (PromotionCategory) TableName() string { return "promotion_category" }
func (Promotion) TableName() string         { return "promotion" }
func (PromotionTier) TableName() string     { return "promotion_tier" }
func (PurchaseLog) TableName() string       { return "purchase_log" }
func (CmsLead) TableName() string           { return "cms_lead" }
func (SolarLead) TableName() string         { return "solar_lead" }
func (TechLead) TableName() string          { return "tech_lead" }
func (TechRegis) TableName() string         { return "tech_regis" }
func (LeadLog) TableName() string           { return "lead_log" }
func (ContactUs) TableName() string         { return "contact_us" }
func (PointSetting) TableName() string      { return "point_setting" }
func (PointTransaction) TableName() string  { return "point_transaction" }
func (TopicCategory) TableName() string     { return "topic_category" }
func (FAQ) TableName() string               { return "faq" }
