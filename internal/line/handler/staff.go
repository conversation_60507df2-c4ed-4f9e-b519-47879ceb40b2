package handler

import (
	"net/http"

	"digital-transformation-api/infrastructure"
	staffdb "digital-transformation-api/internal/line/port/staff-db"
	"digital-transformation-api/internal/line/service/staff"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"

	"github.com/gin-gonic/gin"
)

type staffHandler struct {
	service staff.Service
}

func NewStaffHandler(service staff.Service) *staffHandler {
	return &staffHandler{
		service: service,
	}
}



// GetStaffInfo retrieves staff information using LINE token from header
func (h *staffHandler) GetStaffInfo(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	// Create empty request since token comes from header
	request := &staff.GetStaffInfoRequest{}

	response, err := h.service.GetStaffInfo(request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Bind Staff Routes
func BindStaffRoute(app gins.GinApps) {
	svc := staff.New(
		staffdb.NewAdaptorPG(infrastructure.Db),
	)

	hdl := NewStaffHandler(svc)

	app.Register(http.MethodGet, "/line/staff/get-info", app.ParseRouteContext(hdl.GetStaffInfo))
}