package staff

import (
	"digital-transformation-api/internal/line/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Service interface {
	GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetStaffInfoResponse, errs.Error)
}



// Get Staff Info by LINE Token
type GetStaffInfoRequest struct {
	// No request body needed - token comes from header
}

type GetStaffInfoResponse struct {
	Success bool              `json:"success"`
	Message string            `json:"message,omitempty"`
	Data    *StaffInfoData    `json:"data,omitempty"`
	Version string            `json:"version"`
}

type StaffInfoData struct {
	Staff *domain.Staff `json:"staff"`
}