package staff

import (
	staffdb "digital-transformation-api/internal/line/port/staff-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	staffDb staffdb.Port
}

func New(staffDb staffdb.Port) Service {
	return &service{
		staffDb: staffDb,
	}
}

// GetStaffInfo retrieves staff information using LINE token from header
func (s *service) GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetStaffInfoResponse, errs.Error) {
	// Get LINE token from header
	lineToken := rctx.Header.TokenLine
	if lineToken == "" {
		l.<PERSON>("LINE token is required")
		return &GetStaffInfoResponse{
			Success: false,
			Message: "LINE token is required",
			Version: "1.0.0",
		}, errs.NewCustom(401, "40001", "LINE token is required", "LINE token is required")
	}

	// Get staff token by LINE token
	tokenResp, err := s.staffDb.GetByUserToken(&staffdb.GetByUserTokenRequest{
		UserToken: lineToken,
	}, rctx, l)
	if err != nil {
		l.Errorf("failed to get staff token: %v", err)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Failed to retrieve staff information",
			Version: "1.0.0",
		}, err
	}

	if tokenResp.StaffToken == nil {
		l.Errorf("staff token not found for LINE token")
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Invalid LINE token",
			Version: "1.0.0",
		}, errs.NewCustom(404, "40404", "Staff not found", "Staff not found")
	}

	// Get staff by staff_id from token
	staffResp, err := s.staffDb.GetByID(&staffdb.GetByIDRequest{
		ID: tokenResp.StaffToken.StaffID,
	}, rctx, l)
	if err != nil {
		l.Errorf("failed to get staff by ID: %v", err)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Failed to retrieve staff information",
			Version: "1.0.0",
		}, err
	}

	if staffResp.Staff == nil {
		l.Errorf("staff not found for ID: %s", tokenResp.StaffToken.StaffID)
		return &GetStaffInfoResponse{
			Success: false,
			Message: "Staff not found",
			Version: "1.0.0",
		}, errs.NewCustom(404, "40404", "Staff not found", "Staff not found")
	}

	return &GetStaffInfoResponse{
		Success: true,
		Data: &StaffInfoData{
			Staff: staffResp.Staff,
		},
		Version: "1.0.0",
	}, nil
}





