package domain

import (
	"digital-transformation-api/internal/enum/staff"
	"time"
)

// Staff represents the staff table for LINE service
type Staff struct {
	ID                    string        `gorm:"column:id;primaryKey" json:"id"`
	RoleID                *string       `gorm:"column:role_id" json:"role_id"`
	Permissions           []string      `gorm:"column:permissions;type:text[]" json:"permissions"`
	EmployeeID            *string       `gorm:"column:employee_id" json:"employee_id"`
	FirstName             string        `gorm:"column:firstname" json:"firstname" validate:"required"`
	LastName              string        `gorm:"column:lastname" json:"lastname" validate:"required"`
	Email                 string        `gorm:"column:email" json:"email" validate:"required,email"`
	Password              string        `gorm:"column:password" json:"-"`
	Salt                  string        `gorm:"column:salt" json:"-"`
	Phone                 *string       `gorm:"column:phone" json:"phone"`
	Gender                *staff.Gender `gorm:"column:gender" json:"gender" validate:"omitempty,staff_gender"`
	DateOfBirth           *time.Time    `gorm:"column:dateofbirth" json:"dateofbirth"`
	Education             *string       `gorm:"column:education" json:"education"`
	Job                   *string       `gorm:"column:job" json:"job"`
	ShopID                *string       `gorm:"column:shop_id" json:"shop_id"`
	AreaID                *string       `gorm:"column:area_id" json:"area_id"`
	SupervisorID          *string       `gorm:"column:supervisor_id" json:"supervisor_id"`
	ProfileURL            *string       `gorm:"column:profile_url" json:"profile_url"`
	ProfileURLContentType *string       `gorm:"column:profile_url_content_type" json:"profile_url_content_type"`
	ReferenceCode         *string       `gorm:"column:reference_code" json:"reference_code"`
	Status                staff.Status  `gorm:"column:status" json:"status" validate:"required,staff_status"`
	CreatedAt             time.Time     `gorm:"column:created_at" json:"created_at"`
	CreatedByID           *string       `gorm:"column:created_by_id" json:"created_by_id"`
	CreatedBy             *string       `gorm:"column:created_by" json:"created_by"`
	UpdatedAt             *time.Time    `gorm:"column:updated_at" json:"updated_at"`
	UpdatedByID           *string       `gorm:"column:updated_by_id" json:"updated_by_id"`
	UpdatedBy             *string       `gorm:"column:updated_by" json:"updated_by"`
}

func (Staff) TableName() string {
	return "staff"
}

// StaffToken represents the staff_token table for LINE service
type StaffToken struct {
	ID          string     `gorm:"column:id;primaryKey" json:"id"`
	StaffID     string     `gorm:"column:staff_id" json:"staff_id" validate:"required"`
	Token       string     `gorm:"column:token" json:"token" validate:"required"`
	UpdatedAt   *time.Time `gorm:"column:updated_at" json:"updated_at"`
	UpdatedByID *string    `gorm:"column:updated_by_id" json:"updated_by_id"`
	UpdatedBy   *string    `gorm:"column:updated_by" json:"updated_by"`
}

func (StaffToken) TableName() string {
	return "staff_token"
}