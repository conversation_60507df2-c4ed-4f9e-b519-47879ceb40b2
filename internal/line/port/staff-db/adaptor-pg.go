package staffdb

import (
	"digital-transformation-api/internal/line/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"

	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{
		db: db,
	}
}

// GetByID retrieves staff by ID
func (a *adaptorPG) GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Where("id = ?", request.ID).First(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.<PERSON><PERSON>rf("staff not found: %v", err)
			return nil, errs.NewBusinessError("40404")
		}
		l.<PERSON>("failed to get staff: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByIDResponse{
		Staff: &staff,
	}, nil
}

// GetByUserToken retrieves staff token by LINE token
func (a *adaptorPG) GetByUserToken(request *GetByUserTokenRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByUserTokenResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staffToken domain.StaffToken
	if err := tx.Where("token = ?", request.UserToken).First(&staffToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &GetByUserTokenResponse{StaffToken: nil}, nil
		}
		l.Errorf("failed to get staff token: %v", err)
		return nil, errs.NewInternalError()
	}

	return &GetByUserTokenResponse{
		StaffToken: &staffToken,
	}, nil
}