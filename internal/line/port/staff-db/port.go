package staffdb

import (
	"digital-transformation-api/internal/line/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

// Port defines the database operations needed for LINE staff service
type Port interface {
	GetByID(request *GetByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByIDResponse, errs.Error)
	GetByUserToken(request *GetByUserTokenRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetByUserTokenResponse, errs.Error)
}

// GetByID requests and responses
type GetByIDRequest struct {
	ID string `json:"id"`
}

type GetByIDResponse struct {
	Staff *domain.Staff `json:"staff"`
}

// GetByUserToken requests and responses
type GetByUserTokenRequest struct {
	UserToken string `json:"user_token"`
}

type GetByUserTokenResponse struct {
	StaffToken *domain.StaffToken `json:"staff_token"`
}