package handler

import (
	"net/http"
	"strconv"

	"digital-transformation-api/infrastructure"
	staffenum "digital-transformation-api/internal/enum/staff"
	staffdb "digital-transformation-api/internal/portal/port/staff-db"
	"digital-transformation-api/internal/portal/service/staff"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"

	"github.com/gin-gonic/gin"
)

type staffHandler struct {
	service staff.Service
}

func NewStaffHandler(service staff.Service) *staffHandler {
	return &staffHandler{
		service: service,
	}
}

// Create Staff
func (h *staffHandler) Create(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request staff.CreateRequest
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind create request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	response, err := h.service.Create(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusCreated, response)
}

// Get Staff by ID
func (h *staffHandler) GetByID(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	id := ctx.Param("id")
	if id == "" {
		l.Errorf("staff id is required")
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	request := staff.GetByIDRequest{
		ID: id,
	}

	response, err := h.service.GetByID(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Update Staff
func (h *staffHandler) Update(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	id := ctx.Param("id")
	if id == "" {
		l.Errorf("staff id is required")
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	var request staff.UpdateRequest
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind update request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	request.ID = id

	response, err := h.service.Update(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Delete Staff
func (h *staffHandler) Delete(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	id := ctx.Param("id")
	if id == "" {
		l.Errorf("staff id is required")
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	request := staff.DeleteRequest{
		ID: id,
	}

	response, err := h.service.Delete(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// List Staff
func (h *staffHandler) List(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request staff.ListRequest

	// Parse query parameters
	if pageStr := ctx.Query("page"); pageStr != "" {
		if page, err := strconv.ParseInt(pageStr, 10, 64); err == nil {
			request.Page = page
		}
	}

	if pageSizeStr := ctx.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.ParseInt(pageSizeStr, 10, 64); err == nil {
			request.PageSize = pageSize
		}
	}

	if status := ctx.Query("status"); status != "" {
		staffStatus := staffenum.Status(status)
		request.Status = &staffStatus
	}

	if shopID := ctx.Query("shop_id"); shopID != "" {
		request.ShopID = &shopID
	}

	if areaID := ctx.Query("area_id"); areaID != "" {
		request.AreaID = &areaID
	}

	if search := ctx.Query("search"); search != "" {
		request.Search = &search
	}

	response, err := h.service.List(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Bind Staff Routes
func BindStaffRoute(app gins.GinApps) {
	svc := staff.New(
		staffdb.NewAdaptorPG(infrastructure.Db),
	)

	hdl := NewStaffHandler(svc)

	// Staff CRUD routes
	app.Register(http.MethodPost, "/portal/staff", app.ParseRouteContext(hdl.Create))
	app.Register(http.MethodGet, "/portal/staff/:id", app.ParseRouteContext(hdl.GetByID))
	app.Register(http.MethodPut, "/portal/staff/:id", app.ParseRouteContext(hdl.Update))
	app.Register(http.MethodDelete, "/portal/staff/:id", app.ParseRouteContext(hdl.Delete))
	app.Register(http.MethodGet, "/portal/staff", app.ParseRouteContext(hdl.List))

}
